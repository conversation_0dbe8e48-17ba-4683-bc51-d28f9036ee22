<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - <PERSON> Navigator | 2302900130018</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #f0f2f5;
            overflow: hidden;
        }

        .navigator-container {
            display: grid;
            grid-template-areas:
                "nav nav nav nav"
                "cv institute department tutorial";
            grid-template-rows: 60px 1fr;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            height: 100vh;
            gap: 2px;
            background: #333;
        }

        .navigation-bar {
            grid-area: nav;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-title {
            font-size: 1.5em;
            font-weight: bold;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            font-size: 0.9em;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .nav-btn.active {
            background: rgba(255,255,255,0.4);
        }

        .frame-section {
            background: white;
            overflow: hidden;
            position: relative;
        }

        .cv-frame {
            grid-area: cv;
            padding: 20px;
            overflow-y: auto;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .institute-frame {
            grid-area: institute;
        }

        .department-frame {
            grid-area: department;
        }

        .tutorial-frame {
            grid-area: tutorial;
        }

        .frame-header {
            background: #2c3e50;
            color: white;
            padding: 10px 15px;
            font-weight: bold;
            font-size: 0.9em;
            border-bottom: 2px solid #34495e;
        }

        .frame-content {
            height: calc(100% - 40px);
            overflow: hidden;
        }

        iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        /* CV Styles */
        .cv-header {
            text-align: center;
            margin-bottom: 25px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .profile-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            margin: 0 auto 15px;
        }

        .cv-header h1 {
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 1.8em;
        }

        .cv-header .subtitle {
            color: #7f8c8d;
            font-size: 1em;
            margin-bottom: 15px;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.85em;
            color: #555;
        }

        .cv-section {
            background: white;
            margin-bottom: 15px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .cv-section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }

        .education-item, .experience-item {
            margin-bottom: 15px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #667eea;
        }

        .education-item h3, .experience-item h3 {
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 1em;
        }

        .date {
            color: #7f8c8d;
            font-style: italic;
            font-size: 0.8em;
        }

        .skills-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .skill-tag {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .loading-message {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            font-style: italic;
        }

        @media (max-width: 1200px) {
            .navigator-container {
                grid-template-areas:
                    "nav nav"
                    "cv institute"
                    "department tutorial";
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 60px 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .navigator-container {
                grid-template-areas:
                    "nav"
                    "cv"
                    "institute"
                    "department"
                    "tutorial";
                grid-template-columns: 1fr;
                grid-template-rows: 60px 1fr 1fr 1fr 1fr;
            }

            .nav-title {
                font-size: 1.2em;
            }

            .nav-buttons {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="navigator-container">
        <!-- Navigation Bar -->
        <div class="navigation-bar">
            <div class="nav-title">🎓 Hariom Sharma - Academic Navigator</div>
            <div class="nav-buttons">
                <button class="nav-btn active" onclick="focusFrame('cv')">CV</button>
                <button class="nav-btn" onclick="focusFrame('institute')">Institute</button>
                <button class="nav-btn" onclick="focusFrame('department')">Department</button>
                <button class="nav-btn" onclick="focusFrame('tutorial')">Tutorials</button>
            </div>
        </div>

        <!-- CV Frame -->
        <div class="frame-section cv-frame" id="cv-frame">
            <div class="cv-header">
                <div class="profile-circle">HS</div>
                <h1>Hariom Sharma</h1>
                <p class="subtitle">Information Technology Student | Roll No: 2302900130018</p>
                <div class="contact-grid">
                    <div>📧 <EMAIL></div>
                    <div>📱 +91 XXXXXXXXXX</div>
                    <div>📍 Ghaziabad, UP, India</div>
                    <div>🎓 ABES Institute of Technology</div>
                </div>
            </div>

            <div class="cv-section">
                <h2>Education</h2>
                <div class="education-item">
                    <h3>Bachelor of Technology - Information Technology</h3>
                    <p><strong>ABES Institute of Technology, Ghaziabad</strong></p>
                    <p class="date">2023 - 2027 (Expected)</p>
                    <p>Currently pursuing B.Tech in IT with focus on web development, programming, and software engineering.</p>
                </div>
                <div class="education-item">
                    <h3>Higher Secondary Education</h3>
                    <p><strong>Senior Secondary School</strong></p>
                    <p class="date">2021 - 2023</p>
                    <p>Completed 12th grade with Science stream (PCM with Computer Science)</p>
                </div>
            </div>

            <div class="cv-section">
                <h2>Technical Skills</h2>
                <div class="skills-container">
                    <span class="skill-tag">HTML5</span>
                    <span class="skill-tag">CSS3</span>
                    <span class="skill-tag">JavaScript</span>
                    <span class="skill-tag">Web Development</span>
                    <span class="skill-tag">Programming</span>
                    <span class="skill-tag">Database Management</span>
                    <span class="skill-tag">Software Engineering</span>
                    <span class="skill-tag">Problem Solving</span>
                </div>
            </div>

            <div class="cv-section">
                <h2>Projects & Experience</h2>
                <div class="experience-item">
                    <h3>Web Development Projects</h3>
                    <p class="date">Academic Projects - 2024</p>
                    <p>Developed bookstore management system, user registration/login systems, and responsive web interfaces.</p>
                </div>
                <div class="experience-item">
                    <h3>Academic Coursework</h3>
                    <p class="date">Ongoing - 2023-2027</p>
                    <p>Comprehensive study of IT including Data Structures, Algorithms, DBMS, Web Technologies, and Software Engineering.</p>
                </div>
            </div>
        </div>

