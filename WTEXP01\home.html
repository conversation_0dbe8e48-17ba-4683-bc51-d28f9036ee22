<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> | 2302900130018</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .profile-img {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
            font-weight: bold;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #555;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .cv-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .cv-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
            font-size: 1.8em;
        }

        .education-item, .experience-item {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .education-item h3, .experience-item h3 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .date {
            color: #7f8c8d;
            font-style: italic;
            font-size: 0.9em;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .skill-item {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 25px;
            text-align: center;
            border: 2px solid #667eea;
            color: #2c3e50;
            font-weight: 500;
        }

        .websites-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .websites-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .website-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-decoration: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: block;
        }

        .website-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            text-decoration: none;
            color: white;
        }

        .website-card h3 {
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .website-card p {
            opacity: 0.9;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .contact-info {
                flex-direction: column;
                align-items: center;
                gap: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .websites-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div class="profile-img">HS</div>
            <h1>Hariom Sharma</h1>
            <p class="subtitle">Information Technology Student | Roll No: 2302900130018</p>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📧</span>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span>📱</span>
                    <span>+91 XXXXXXXXXX</span>
                </div>
                <div class="contact-item">
                    <span>📍</span>
                    <span>Ghaziabad, UP, India</span>
                </div>
            </div>
        </div>

        <!-- Main CV Content -->
        <div class="main-content">
            <!-- Education Section -->
            <div class="cv-section">
                <h2>Education</h2>
                <div class="education-item">
                    <h3>Bachelor of Technology - Information Technology</h3>
                    <p><strong>ABES Institute of Technology, Ghaziabad</strong></p>
                    <p class="date">2023 - 2027 (Expected)</p>
                    <p>Currently pursuing B.Tech in Information Technology with focus on web development, programming, and software engineering.</p>
                </div>
                <div class="education-item">
                    <h3>Higher Secondary Education</h3>
                    <p><strong>Senior Secondary School</strong></p>
                    <p class="date">2021 - 2023</p>
                    <p>Completed 12th grade with Science stream (PCM with Computer Science)</p>
                </div>
            </div>

            <!-- Skills Section -->
            <div class="cv-section">
                <h2>Technical Skills</h2>
                <div class="skills-grid">
                    <div class="skill-item">HTML5</div>
                    <div class="skill-item">CSS3</div>
                    <div class="skill-item">JavaScript</div>
                    <div class="skill-item">Web Development</div>
                    <div class="skill-item">Programming</div>
                    <div class="skill-item">Database Management</div>
                    <div class="skill-item">Software Engineering</div>
                    <div class="skill-item">Problem Solving</div>
                </div>
            </div>
        </div>

        <!-- Projects Section -->
        <div class="cv-section">
            <h2>Projects & Experience</h2>
            <div class="experience-item">
                <h3>Web Development Projects</h3>
                <p class="date">Academic Projects - 2024</p>
                <p>Developed various web applications including bookstore management system, user registration and login systems, and responsive web interfaces using HTML, CSS, and JavaScript.</p>
            </div>
            <div class="experience-item">
                <h3>Academic Coursework</h3>
                <p class="date">Ongoing - 2023-2027</p>
                <p>Comprehensive study of Information Technology including Data Structures, Algorithms, Database Management Systems, Web Technologies, and Software Engineering principles.</p>
            </div>
        </div>

        <!-- Websites Section -->
        <div class="websites-section">
            <h2>Important Academic Links</h2>
            <div class="websites-grid">
                <a href="https://www.abesit.in/" target="_blank" class="website-card">
                    <h3>🏛️ ABES Institute of Technology</h3>
                    <p>Official website of ABES Institute of Technology, Ghaziabad. Access academic information, announcements, faculty details, and institutional resources.</p>
                </a>

                <a href="https://www.abesit.in/departments/department-of-information-technology/" target="_blank" class="website-card">
                    <h3>💻 IT Department - ABESIT</h3>
                    <p>Department of Information Technology at ABES Institute. Find curriculum details, faculty information, lab facilities, and department-specific updates.</p>
                </a>

                <a href="https://www.khanacademy.org/" target="_blank" class="website-card">
                    <h3>📚 Khan Academy</h3>
                    <p>Free online learning platform offering courses in mathematics, science, computer programming, and various other subjects for skill enhancement.</p>
                </a>

                <a href="https://www.khanacademy.org/computing/computer-programming" target="_blank" class="website-card">
                    <h3>⌨️ Computer Programming Tutorials</h3>
                    <p>Comprehensive computer programming tutorials covering JavaScript, HTML/CSS, SQL, and other programming languages with interactive exercises.</p>
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="cv-section" style="text-align: center;">
            <p style="color: #7f8c8d; font-style: italic;">
                "Passionate about technology and committed to continuous learning in the field of Information Technology."
            </p>
        </div>
    </div>
</body>
</html>

