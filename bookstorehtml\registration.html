<html>
<head>
    <title>Hariom SHARMA 2302900130018</title>
 <script language="javascript">
        function button_click() {
            var frame = f.frame.value;
            var spchar = "!@#$%^&*()+=-[]\\\";,./{}|:<>?~";
            var number = "0123456789";

            // Check if the username is at least 6 characters long
            if (frame.length < 6) {
                alert("First name must be at least 6 characters");
                return false;
            }

            // Check for special characters
            for (var i = 0; i < frame.length; i++) {
                if (spchar.indexOf(frame.charAt(i)) != -1) {
                    alert("User  ID should not have special characters");
                    f.frame.value = "";
                    return false;
                }
            }

            // Check for numbers
            for (var i = 0; i < frame.length; i++) {
                if (number.indexOf(frame.charAt(i)) != -1) {
                    alert("User  ID should not have numbers");
                    f.frame.value = "";
                    return false;
                }
            }

            // If all checks pass
            alert("Valid username");
            return true;
        }
    </script>
<script type="text/javascript">
        function pass() {
            var pw = document.f.pw.value;
            var cpw = document.f.cpw.value;

            if (pw.length < 6) {
                alert("PASSWORD MUST BE 6 CHARACTERS");
                return; // Stop further checks if password is too short
            }
            if (pw !== cpw) {
                alert("PASSWORD DON'T MATCH");
                return; // Stop further checks if passwords don't match
            }
            alert("PASSWORD VALIDATION SUCCESS");
        }
    </script>

</head>
<body bgcolor="#ffc0c8">
    <center > <font COLOR="#800000" size="150px"><U>REGISTRATION FORM</U></font> </center>
    <BR></BR><BR></BR><BR></BR><BR>
    <CENTER ><form action="right.html">
        Full Name:
        <input type="text" id="fullname" placeholder="Full name">
        <br><br>
        Email:
        <input type="text" id="email"  placeholder="Give your email">
        <br><br>
        Gender:<input type='radio'name="gender">Male<input type='radio'name="gender">Female<br/><br/>
	Date Of Birth: <input type="date">
        <br><br>
        Phone Number : <input type="number">
        <br><br>
        Address: <input type="text" id="address">
        <br><br>
        Password: <input type="text" id="password" required>
        <br><br>
        <button>SUBMIT</button>
        <button>RESET</button>

    </form></CENTER>
    
</body>
</html>