<!DOCTYPE html>
<html>
<head>
    <title>ANKIT SRVASTAV</title>
    <script type="text/javascript">
        function pass() {
            var pw = document.f.pw.value;
            var cpw = document.f.cpw.value;

            if (pw.length < 6) {
                alert("PASSWORD MUST BE 6 CHARACTERS");
                return; // Stop further checks if password is too short
            }
            if (pw !== cpw) {
                alert("PASSWORD DON'T MATCH");
                return; // Stop further checks if passwords don't match
            }
            alert("PASSWORD VALIDATION SUCCESS");
        }
    </script>
</head>
<body>
    <form name="f">
        <table align="center">
            <h2 align="center">PASSWORD VALIDATION</h2>
            <br>
            <tr>
                <td align="right">Password:</td>
                <td align="left"><input type="password" maxlength="10" size="30" name="pw"></td>
            </tr>
            <tr>
                <td align="right">Confirm Password:</td>
                <td align="left"><input type="password" maxlength="10" size="30" name="cpw"></td>
            </tr>
            <tr>
                <td></td>
                <td><input type="button" value="SUBMIT" onclick="pass()"></td>
            </tr>
        </table>
    </form>
</body>
</html>