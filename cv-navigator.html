<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Academic Navigator | 2302900130018</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #f0f2f5;
            overflow: hidden;
        }

        .navigator-container {
            display: grid;
            grid-template-areas: 
                "nav nav nav nav"
                "cv institute department tutorial";
            grid-template-rows: 70px 1fr;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            height: 100vh;
            gap: 3px;
            background: #2c3e50;
        }

        .navigation-bar {
            grid-area: nav;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 25px;
            color: white;
            box-shadow: 0 3px 15px rgba(0,0,0,0.2);
        }

        .nav-title {
            font-size: 1.6em;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 12px;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 10px 18px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.95em;
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .nav-btn.active {
            background: rgba(255,255,255,0.4);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .frame-section {
            background: white;
            overflow: hidden;
            position: relative;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .cv-frame {
            grid-area: cv;
            padding: 20px;
            overflow-y: auto;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .institute-frame {
            grid-area: institute;
        }

        .department-frame {
            grid-area: department;
        }

        .tutorial-frame {
            grid-area: tutorial;
        }

        .frame-header {
            background: #2c3e50;
            color: white;
            padding: 12px 18px;
            font-weight: bold;
            font-size: 1em;
            border-bottom: 3px solid #34495e;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .frame-content {
            height: calc(100% - 48px);
            overflow: hidden;
        }

        iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        /* CV Styles */
        .cv-header {
            text-align: center;
            margin-bottom: 25px;
            padding: 25px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .profile-circle {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            font-weight: bold;
            margin: 0 auto 18px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .cv-header h1 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 2em;
        }

        .cv-header .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
            margin-bottom: 18px;
            font-weight: 500;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            font-size: 0.9em;
            color: #555;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .cv-section {
            background: white;
            margin-bottom: 18px;
            padding: 22px;
            border-radius: 10px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.08);
        }

        .cv-section h2 {
            color: #2c3e50;
            margin-bottom: 18px;
            font-size: 1.4em;
            border-bottom: 3px solid #667eea;
            padding-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .education-item, .experience-item {
            margin-bottom: 16px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .education-item h3, .experience-item h3 {
            color: #2c3e50;
            margin-bottom: 6px;
            font-size: 1.1em;
        }

        .date {
            color: #7f8c8d;
            font-style: italic;
            font-size: 0.85em;
            margin-bottom: 8px;
        }

        .skills-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 12px;
        }

        .skill-tag {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 6px 14px;
            border-radius: 18px;
            font-size: 0.85em;
            font-weight: 500;
            box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
        }

        .loading-message {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            font-style: italic;
            font-size: 1.1em;
        }

        @media (max-width: 1200px) {
            .navigator-container {
                grid-template-areas: 
                    "nav nav"
                    "cv institute"
                    "department tutorial";
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 70px 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .navigator-container {
                grid-template-areas: 
                    "nav"
                    "cv"
                    "institute"
                    "department"
                    "tutorial";
                grid-template-columns: 1fr;
                grid-template-rows: 70px 1fr 1fr 1fr 1fr;
            }
            
            .nav-title {
                font-size: 1.3em;
            }
            
            .nav-buttons {
                display: none;
            }
            
            .contact-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script>
        function focusFrame(frameId) {
            // Remove active class from all buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Add active class to clicked button
            event.target.classList.add('active');
            
            // Scroll to frame on mobile
            if (window.innerWidth <= 768) {
                document.getElementById(frameId + '-frame').scrollIntoView({
                    behavior: 'smooth'
                });
            }
        }
    </script>
</head>
<body>
    <div class="navigator-container">
        <!-- Navigation Bar -->
        <div class="navigation-bar">
            <div class="nav-title">🎓 Hariom Sharma - Academic Navigator</div>
            <div class="nav-buttons">
                <button class="nav-btn active" onclick="focusFrame('cv')">CV</button>
                <button class="nav-btn" onclick="focusFrame('institute')">Institute</button>
                <button class="nav-btn" onclick="focusFrame('department')">Department</button>
                <button class="nav-btn" onclick="focusFrame('tutorial')">Tutorials</button>
            </div>
        </div>

        <!-- CV Frame -->
        <div class="frame-section cv-frame" id="cv-frame">
            <div class="cv-header">
                <div class="profile-circle">HS</div>
                <h1>Hariom Sharma</h1>
                <p class="subtitle">Information Technology Student | Roll No: 2302900130018</p>
                <div class="contact-grid">
                    <div class="contact-item">
                        <span>📧</span>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <span>📱</span>
                        <span>+91 XXXXXXXXXX</span>
                    </div>
                    <div class="contact-item">
                        <span>📍</span>
                        <span>Ghaziabad, UP, India</span>
                    </div>
                    <div class="contact-item">
                        <span>🎓</span>
                        <span>ABES Institute of Technology</span>
                    </div>
                </div>
            </div>

            <div class="cv-section">
                <h2>🎓 Education</h2>
                <div class="education-item">
                    <h3>Bachelor of Technology - Information Technology</h3>
                    <p><strong>ABES Institute of Technology, Ghaziabad</strong></p>
                    <p class="date">2023 - 2027 (Expected)</p>
                    <p>Currently pursuing B.Tech in IT with focus on web development, programming, and software engineering.</p>
                </div>
                <div class="education-item">
                    <h3>Higher Secondary Education</h3>
                    <p><strong>Senior Secondary School</strong></p>
                    <p class="date">2021 - 2023</p>
                    <p>Completed 12th grade with Science stream (PCM with Computer Science)</p>
                </div>
            </div>

            <div class="cv-section">
                <h2>💻 Technical Skills</h2>
                <div class="skills-container">
                    <span class="skill-tag">HTML5</span>
                    <span class="skill-tag">CSS3</span>
                    <span class="skill-tag">JavaScript</span>
                    <span class="skill-tag">Web Development</span>
                    <span class="skill-tag">Programming</span>
                    <span class="skill-tag">Database Management</span>
                    <span class="skill-tag">Software Engineering</span>
                    <span class="skill-tag">Problem Solving</span>
                    <span class="skill-tag">Data Structures</span>
                    <span class="skill-tag">Algorithms</span>
                </div>
            </div>

            <div class="cv-section">
                <h2>🚀 Projects & Experience</h2>
                <div class="experience-item">
                    <h3>Web Development Projects</h3>
                    <p class="date">Academic Projects - 2024</p>
                    <p>Developed bookstore management system, user registration/login systems, and responsive web interfaces using HTML, CSS, and JavaScript.</p>
                </div>
                <div class="experience-item">
                    <h3>Academic Coursework</h3>
                    <p class="date">Ongoing - 2023-2027</p>
                    <p>Comprehensive study of IT including Data Structures, Algorithms, DBMS, Web Technologies, and Software Engineering principles.</p>
                </div>
                <div class="experience-item">
                    <h3>Learning Goals</h3>
                    <p class="date">2024-2025</p>
                    <p>Focusing on advanced web development, full-stack programming, and modern software development practices through FreeCodeCamp and other platforms.</p>
                </div>
            </div>
        </div>

        <!-- Institute Frame -->
        <div class="frame-section institute-frame" id="institute-frame">
            <div class="frame-header">
                <span>🏛️</span>
                <span>ABES Institute of Technology</span>
            </div>
            <div class="frame-content">
                <iframe src="https://www.abesit.in/"
                        title="ABES Institute of Technology"
                        loading="lazy">
                    <div class="loading-message">Loading ABES Institute website...</div>
                </iframe>
            </div>
        </div>

        <!-- Department Frame -->
        <div class="frame-section department-frame" id="department-frame">
            <div class="frame-header">
                <span>💻</span>
                <span>IT Department - ABESIT</span>
            </div>
            <div class="frame-content">
                <iframe src="https://www.abesit.in/departments/department-of-information-technology/"
                        title="IT Department - ABES Institute"
                        loading="lazy">
                    <div class="loading-message">Loading IT Department website...</div>
                </iframe>
            </div>
        </div>

        <!-- Tutorial Frame -->
        <div class="frame-section tutorial-frame" id="tutorial-frame">
            <div class="frame-header">
                <span>📚</span>
                <span>FreeCodeCamp - Programming Tutorials</span>
            </div>
            <div class="frame-content">
                <iframe src="https://www.freecodecamp.org/"
                        title="FreeCodeCamp - Learn to Code"
                        loading="lazy">
                    <div class="loading-message">Loading FreeCodeCamp tutorials...</div>
                </iframe>
            </div>
        </div>
    </div>
</body>
</html>
