<!DOCTYPE html>
<html>
<head>
    <title>ANKIT SRIVASTAV 2302900130018 </title>
    <script language="javascript">
        function button_click() {
            var frame = f.frame.value;
            var spchar = "!@#$%^&*()+=-[]\\\";,./{}|:<>?~";
            var number = "0123456789";

            // Check if the username is at least 6 characters long
            if (frame.length < 6) {
                alert("First name must be at least 6 characters");
                return false;
            }

            // Check for special characters
            for (var i = 0; i < frame.length; i++) {
                if (spchar.indexOf(frame.charAt(i)) != -1) {
                    alert("User  ID should not have special characters");
                    f.frame.value = "";
                    return false;
                }
            }

            // Check for numbers
            for (var i = 0; i < frame.length; i++) {
                if (number.indexOf(frame.charAt(i)) != -1) {
                    alert("User  ID should not have numbers");
                    f.frame.value = "";
                    return false;
                }
            }

            // If all checks pass
            alert("Valid username");
            return true;
        }
    </script>
</head>
<body>
    <center>
        <form name="f">
            <table>
                <h2 align="center">USERNAME VALIDATION</h2>
                <tr>
                    <td align="right">USERNAME:</td>
                    <td><input type="text" name="frame" maxlength="20" size="30"></td>
                </tr>
                <tr>
                    <td></td>
                    <td><input type="button" value="SUBMIT" onclick="button_click()"></td>
                </tr>
            </table>
        </form>
    </center>
</body>
</html>